# Dependencies
node_modules/
*/node_modules/
bun.lock
*/bun.lock

# Build outputs
dist/
*/dist/
build/
*/build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
*/.env
*/.env.*

# Database files
*.db
*.sqlite
*.sqlite3
database.db
*/database.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Upload directories
uploads/
*/uploads/
public/uploads/

# Config files with sensitive data
config/database.js
config/production.js
*/config/database.js
*/config/production.js

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup

# Test files
test-results/
coverage/

# Local development files
.local
*.local

# Bun specific
.bun

# Vite specific
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Vue specific
*.vue.js
*.vue.ts

# Tailwind CSS
tailwind.config.js.timestamp-*

# MySQL dumps
*.sql.gz
*.sql.bz2
backup.sql
dump.sql

# Project specific
forum_tables.sql.bak
sql.sql.bak
